<style>
  .select2-container .select2-selection--multiple .select2-selection__choice {
    color: #000000 !important;
  }
</style>
<%= form_for([:operation, @firmware]) do |f| %>
<div class="row">
  <div class="col-md-6">
    <div class="field">
      <span style="color:red">*</span> <%= f.label :device_id, "键盘型号" %><br>
      <%= f.select :device_id, Device.all.collect {|o| [o.name, o.pid ]}, { selected: f.object.device_id.to_i }, class: 'form-select', required: true %>
      <%= f.object.device_id %>
    </div>
    <div class="field">
      <span style="color:red">*</span> <%= f.label :version, "版本号" %><br>
      <%= f.text_field :version, class: 'form-control', required: true %>
    </div>
    <div class="field">
      <span style="color:red">*</span> <%= f.label :file, "固件文件(UF2格式)" %><br>
      <%= f.file_field :file, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :force_update, "强制更新" %>
      <%= f.check_box :force_update, class: 'form-check-input' %>
    </div>
    <div class="field">
      <span style="color:red">*</span> <%= f.label :description_zh, "更新说明(中文)" %><br>
      <%= f.text_area :description_zh, class: 'form-control', required: true %>
    </div>
    <div class="field">
      <%= f.label :description_tw, "更新说明(繁体)" %><br>
      <%= f.text_area :description_tw, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_en, "更新说明(英文)" %><br>
      <%= f.text_area :description_en, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_ja, "更新说明(日文)" %><br>
      <%= f.text_area :description_ja, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_ko, "更新说明(韩文)" %><br>
      <%= f.text_area :description_ko, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :description_pt, "更新说明(葡萄牙语)" %><br>
      <%= f.text_area :description_pt, class: 'form-control' %>
    </div>
    <div class="field">
      <%= f.label :deploy_env, "发布环境" %><br>
      <%= f.select :deploy_env,
          Firmware.deploy_envs.keys.map(&:to_s).map { |key| [key.humanize, key] },
          { selected: f.object.deploy_env },
          { multiple: true, class: 'form-control select2' } %>
    </div>
    <div class="actions mt-2">
      <%= f.submit "保存", class: "btn btn-primary", style: "margin-right: 6px;" %>
      <%= link_to '取消', operation_firmwares_path, class: "btn btn-secondary" %>
    </div>
  </div>
</div>

<% end %>

<script>
  $(document).ready(function() {
    $('.select2').select2();
  });
</script>
