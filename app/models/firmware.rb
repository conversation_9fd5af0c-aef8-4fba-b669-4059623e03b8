# == Schema Information
#
# Table name: firmwares
#
#  id             :bigint           not null, primary key
#  deploy_env     :json
#  description_en :text(65535)
#  description_ja :text(65535)
#  description_ko :text(65535)
#  description_pt :text(65535)
#  description_tw :text(65535)
#  description_zh :text(65535)
#  file           :string(255)
#  force_update   :boolean
#  title_en       :string(255)
#  title_ja       :string(255)
#  title_ko       :string(255)
#  title_tw       :string(255)
#  title_zh       :string(255)
#  version        :string(255)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  device_id      :integer
#
class Firmware < ApplicationRecord
  validates :device_id, presence: true

  mount_uploader :file, FileUploader

  # enum device_id: {
  #   EZ80: 9010,
  #   EZ63_IQ: 25344,
  #   EZ75_IQ: 29952,
  #   EV63: 25569
  # }

  def self.get_real_device_id(device_id)
    device = Device.find_by(pid: device_id) || Device.find_by(dfu_pid: device_id)
    device&.pid
    # case device_id
    # when 9010, 4660
    #   return 9010
    # when 25344, 25345
    #   return 25344
    # when 29952, 29953
    #   return 29952
    # when 25569, 25570
    #   return 25569
    # end
  end

  def self.deploy_envs
    {
      dev: 1,
      alpha: 2,
      beta: 3,
      production: 4
    }
  end
end
